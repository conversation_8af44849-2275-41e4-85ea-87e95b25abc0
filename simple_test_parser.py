#!/usr/bin/env python3
"""
Тестовый парсер CPA Quebec Directory - с выбором категории "Individuals"
"""
import asyncio
import json
import logging
import os
from datetime import datetime
from pathlib import Path

import aiohttp
from dotenv import load_dotenv
from playwright.async_api import async_playwright

# Загружаем переменные окружения
load_dotenv()

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger("test_quebec_parser")

# Константы
BASE_URL = "https://cpaquebec.ca/en/find-a-cpa/cpa-directory/"
OUTPUT_DIR = Path("output")
ANTICAPTCHA_KEY = os.getenv("ANTICAPTCHA_API_KEY")

# Глобальная сессия для Anti-Captcha
anticaptcha_session = None

async def get_anticaptcha_session():
    """Получает или создает сессию для Anti-Captcha API"""
    global anticaptcha_session
    if anticaptcha_session is None or anticaptcha_session.closed:
        anticaptcha_session = aiohttp.ClientSession()
    return anticaptcha_session

async def anticaptcha_request(method: str, **payload):
    """Выполняет запрос к Anti-Captcha API"""
    url = f"https://api.anti-captcha.com/{method}"
    session = await get_anticaptcha_session()

    if "clientKey" not in payload and ANTICAPTCHA_KEY:
        payload["clientKey"] = ANTICAPTCHA_KEY

    logger.debug(f"Anti-Captcha request: {method}, payload_keys: {list(payload.keys())}")
    try:
        async with session.post(url, json=payload, timeout=30) as resp:
            # Логируем HTTP статус
            logger.debug(f"Anti-Captcha response status ({method}): {resp.status}")

            response_json = await resp.json()
            # Логируем полный ответ API
            logger.debug(f"Anti-Captcha full response ({method}): {json.dumps(response_json, indent=2)}")

            # Проверяем на ошибки в ответе JSON, даже если статус HTTP 200
            if response_json.get("errorId", 0) != 0:
                error_code = response_json.get("errorCode", "UNKNOWN_API_ERROR")
                error_description = response_json.get("errorDescription", "No description from API.")
                logger.error(f"Anti-Captcha API error in response ({method}): {error_code} - {error_description}")
                # Возвращаем структуру ошибки, чтобы ее можно было обработать выше
                return {"errorId": response_json["errorId"], "errorCode": error_code, "errorDescription": error_description}

            resp.raise_for_status() # Это вызовет исключение для HTTP ошибок 4xx/5xx
            return response_json
    except aiohttp.ClientResponseError as e:
        logger.error(f"Anti-Captcha HTTP error ({method}, status={e.status}): {e.message}")
        error_body = await e.response.text() if e.response else "No response body"
        logger.error(f"Anti-Captcha HTTP error body: {error_body}")
        return {"errorId": 1, "errorCode": f"HTTP_{e.status}", "errorDescription": f"{e.message} - Body: {error_body[:200]}"}
    except asyncio.TimeoutError:
        logger.error(f"Anti-Captcha API timeout ({method})")
        return {"errorId": 1, "errorCode": "TIMEOUT_ERROR", "errorDescription": "API request timed out"}
    except Exception as e:
        logger.error(f"Generic error in Anti-Captcha API request ({method}): {e}", exc_info=True)
        return {"errorId": 1, "errorCode": "REQUEST_EXCEPTION", "errorDescription": str(e)}

async def check_anticaptcha_balance():
    """Проверяет баланс Anti-Captcha."""
    if not ANTICAPTCHA_KEY:
        logger.debug("ANTICAPTCHA_KEY не установлен, проверка баланса пропущена.")
        return True # Считаем, что все в порядке, если ключ не используется

    logger.info("Проверка баланса Anti-Captcha...")
    response = await anticaptcha_request("getBalance")

    if response.get("errorId", 0) != 0:
        logger.error(f"Не удалось проверить баланс Anti-Captcha: {response.get('errorDescription')}")
        return False # Ошибка при проверке баланса

    balance = response.get("balance")
    if balance is not None:
        logger.info(f"Текущий баланс Anti-Captcha: ${balance}")
        if float(balance) < 0.1: # Порог предупреждения
            logger.warning(f"НИЗКИЙ БАЛАНС Anti-Captcha: ${balance}. Рекомендуется пополнить.")
        return True
    else:
        logger.error("Не удалось получить значение баланса из ответа Anti-Captcha.")
        return False

async def select_category(page, category_name="Individuals"):
    """Выбирает категорию на странице"""
    logger.info(f"Выбираем категорию: {category_name}")

    # Сначала снимаем все чекбоксы
    await page.evaluate("""
        () => {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    checkbox.checked = false;
                    checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log('Снят чекбокс:', checkbox.id || checkbox.name);
                }
            });
        }
    """)

    await page.wait_for_timeout(2000)

    # Выбираем нужную категорию
    category_selected = await page.evaluate(f"""
        (categoryName) => {{
            // Ищем по тексту метки
            const labels = document.querySelectorAll('label');
            for (const label of labels) {{
                if (label.textContent.includes(categoryName)) {{
                    console.log('Найдена метка для категории:', categoryName);

                    // Если есть for атрибут
                    if (label.htmlFor) {{
                        const checkbox = document.getElementById(label.htmlFor);
                        if (checkbox) {{
                            checkbox.checked = true;
                            checkbox.dispatchEvent(new Event('change', {{ bubbles: true }}));
                            console.log('Отмечен чекбокс через ID:', checkbox.id);
                            return true;
                        }}
                    }}

                    // Ищем чекбокс внутри метки
                    const checkbox = label.querySelector('input[type="checkbox"]');
                    if (checkbox) {{
                        checkbox.checked = true;
                        checkbox.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        console.log('Отмечен чекбокс внутри метки');
                        return true;
                    }}
                }}
            }}

            // Альтернативный поиск по ID содержащему категорию
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            for (const checkbox of checkboxes) {{
                if (checkbox.id && checkbox.id.includes(categoryName)) {{
                    checkbox.checked = true;
                    checkbox.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    console.log('Отмечен чекбокс по ID содержащему', categoryName + ':', checkbox.id);
                    return true;
                }}
                if (checkbox.name && checkbox.name.includes(categoryName)) {{
                    checkbox.checked = true;
                    checkbox.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    console.log('Отмечен чекбокс по name содержащему', categoryName + ':', checkbox.name);
                    return true;
                }}
            }}

            console.error('Не удалось найти чекбокс для категории:', categoryName);
            return false;
        }}
    """, category_name)

    if category_selected:
        # Также отмечаем чекбокс "Accepting new clients"
        accepting_clients_selected = await page.evaluate("""
            () => {
                const labels = document.querySelectorAll('label');
                for (const label of labels) {
                    if (label.textContent.includes('Accepting new clients')) {
                        console.log('Найдена метка "Accepting new clients"');

                        // Если есть for атрибут
                        if (label.htmlFor) {
                            const checkbox = document.getElementById(label.htmlFor);
                            if (checkbox) {
                                checkbox.checked = true;
                                checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                                console.log('Отмечен чекбокс "Accepting new clients" через ID:', checkbox.id);
                                return true;
                            }
                        }

                        // Ищем чекбокс внутри метки
                        const checkbox = label.querySelector('input[type="checkbox"]');
                        if (checkbox) {
                            checkbox.checked = true;
                            checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                            console.log('Отмечен чекбокс "Accepting new clients" внутри метки');
                            return true;
                        }
                    }
                }

                console.log('Чекбокс "Accepting new clients" не найден');
                return false;
            }
        """)

        if accepting_clients_selected:
            logger.info("Чекбокс 'Accepting new clients' также отмечен")
        else:
            logger.warning("Не удалось отметить чекбокс 'Accepting new clients'")

    if category_selected:
        logger.info(f"Категория '{category_name}' успешно выбрана")
        await page.wait_for_timeout(3000)  # Ждем обновления формы
        return True
    else:
        logger.error(f"Не удалось выбрать категорию '{category_name}'")
        return False

async def solve_captcha_simple(page):
    """Простое решение капчи через Anti-Captcha с ужесточенными проверками"""
    if not ANTICAPTCHA_KEY:
        logger.warning("ANTICAPTCHA_API_KEY не установлен, используем ручное решение")
        input("Решите капчу вручную и нажмите Enter для продолжения...")
        return True

    # Проверка баланса перед началом
    if not await check_anticaptcha_balance():
        logger.error("Проверка баланса Anti-Captcha не удалась или баланс слишком низкий. Решение капчи прервано.")
        return False

    # Механизм повторных попыток
    max_retries = 3
    for attempt_num in range(max_retries): # Изменено имя переменной
        logger.info(f"Попытка решения капчи {attempt_num + 1}/{max_retries}...")
        try:
            # Сначала проверяем, есть ли капча на странице
            captcha_present = await page.evaluate("""
                () => {
                    const recaptchaDiv = document.querySelector('.g-recaptcha');
                    const recaptchaIframes = document.querySelectorAll('iframe[src*="recaptcha"]');
                    return recaptchaDiv !== null || recaptchaIframes.length > 0;
                }
            """)

            if not captcha_present:
                logger.info("Капча не найдена на странице")
                return True

            logger.info("Капча обнаружена на странице")

            # Очищаем любые существующие токены
            await page.evaluate("""
                () => {
                    const existingToken = document.querySelector('#g-recaptcha-response');
                    if (existingToken) {
                        existingToken.value = '';
                        console.log('Очищен существующий токен');
                    }
                }
            """)

            existing_token = await page.evaluate("""
                () => {
                    const response = document.querySelector('#g-recaptcha-response');
                    return response ? response.value : null;
                }
            """)

            if existing_token and len(existing_token) > 50:
                logger.warning(f"ВНИМАНИЕ: Найден существующий токен длиной {len(existing_token)}! Возможно кэш.")
                logger.info("Принудительно очищаем токен...")
                await page.evaluate("""
                    () => {
                        const response = document.querySelector('#g-recaptcha-response');
                        if (response) {
                            response.remove();
                        }
                        const hiddenFields = document.querySelectorAll('input[name="g-recaptcha-response"]');
                        hiddenFields.forEach(field => field.remove());
                    }
                """)

            sitekey = await page.evaluate("""
                () => {
                    const recaptchaDiv = document.querySelector('.g-recaptcha');
                    if (recaptchaDiv && recaptchaDiv.getAttribute('data-sitekey')) {
                        return recaptchaDiv.getAttribute('data-sitekey');
                    }
                    const iframes = document.querySelectorAll('iframe[src*="recaptcha"]');
                    for (const iframe of iframes) {
                        const src = iframe.src;
                        const match = src.match(/k=([^&]+)/);
                        if (match) {
                            return match[1];
                        }
                    }
                    return null;
                }
            """)

            if not sitekey:
                logger.error("Не удалось найти sitekey, переход к следующей попытке...")
                await asyncio.sleep(2)
                continue # К следующей основной попытке

            logger.info(f"Найден sitekey: {sitekey[:10]}...")

            checkbox_state = await page.evaluate("""
                () => {
                    const iframes = document.querySelectorAll('iframe[src*="api2/anchor"]');
                    if (iframes.length > 0) {
                        try {
                            const iframe = iframes[0];
                            const frame = iframe.contentDocument || iframe.contentWindow.document;
                            const checkbox = frame.querySelector('#recaptcha-anchor');
                            return checkbox ? checkbox.getAttribute('aria-checked') : 'unknown';
                        } catch (e) {
                            return 'cross-origin';
                        }
                    }
                    return 'no-iframe';
                }
            """)
            logger.info(f"Состояние чекбокса reCAPTCHA: {checkbox_state}")

            logger.info("Отправляем задачу в Anti-Captcha...")
            task_payload = {
                "task": {
                    "type": "NoCaptchaTaskProxyless",
                    "websiteURL": BASE_URL,
                    "websiteKey": sitekey,
                }
            }

            response_create_task = await anticaptcha_request("createTask", **task_payload)
            if response_create_task.get("errorId", 0) != 0:
                logger.error(f"Ошибка создания задачи Anti-Captcha: {response_create_task.get('errorDescription')}, переход к следующей попытке...")
                await asyncio.sleep(2)
                continue # К следующей основной попытке

            task_id = response_create_task.get("taskId")
            if not task_id:
                logger.error("Не удалось создать задачу Anti-Captcha (нет taskId в ответе), переход к следующей попытке...")
                await asyncio.sleep(2)
                continue # К следующей основной попытке

            logger.info(f"Задача Anti-Captcha создана, ID: {task_id}")

            start_time_task = asyncio.get_event_loop().time() # Используем другое имя переменной
            await asyncio.sleep(10)

            task_solved_successfully = False
            for poll_attempt in range(50): # Изменено имя переменной
                current_time = asyncio.get_event_loop().time()
                elapsed = current_time - start_time_task

                response_get_result = await anticaptcha_request("getTaskResult", taskId=task_id)

                if response_get_result.get("errorId", 0) != 0:
                    logger.error(f"Ошибка получения результата задачи Anti-Captcha: {response_get_result.get('errorDescription')}")
                    if response_get_result.get("errorCode") == "ERROR_KEY_DOES_NOT_EXIST":
                        logger.critical("Ключ Anti-Captcha не существует. Прерывание всех попыток.")
                        return False # Прерываем все
                    # Для других ошибок просто ждем и пробуем еще раз получить результат
                    await asyncio.sleep(5)
                    continue # К следующей попытке получения результата

                if response_get_result.get("status") == "ready":
                    solution = response_get_result.get("solution")
                    token = solution.get("gRecaptchaResponse") if solution else None

                    if token and len(token) > 50:
                        logger.info(f"Получен токен от Anti-Captcha за {elapsed:.1f}с, длина: {len(token)}")
                        if not token.startswith("03"): # Эта проверка может быть не всегда актуальна
                             logger.warning(f"Токен имеет формат, отличный от ожидаемого (не начинается с '03'): {token[:20]}...")

                        # Вставляем токен и делаем кнопки активными
                        await page.evaluate(f"""
                            (token) => {{
                                let response_el = document.querySelector('#g-recaptcha-response');
                                if (!response_el) {{
                                    response_el = document.createElement('textarea');
                                    response_el.id = 'g-recaptcha-response';
                                    response_el.name = 'g-recaptcha-response';
                                    response_el.style.display = 'none';
                                    document.body.appendChild(response_el);
                                }}
                                response_el.value = token;
                                response_el.dispatchEvent(new Event('change', {{ bubbles: true }}));

                                // Принудительно активируем все кнопки отправки
                                const buttons = document.querySelectorAll('button[type="submit"], input[type="submit"]');
                                buttons.forEach(btn => {{
                                    btn.disabled = false;
                                    btn.removeAttribute('disabled');
                                    // Применяем стили, как будто кнопка активна
                                    btn.classList.remove('disabled');
                                    btn.style.opacity = '1';
                                    btn.style.cursor = 'pointer';
                                    console.log('Активирована кнопка:', btn);
                                }});

                                // Если форма содержит reCAPTCHA, отмечаем ее как решенную
                                const recaptchaDiv = document.querySelector('.g-recaptcha');
                                if (recaptchaDiv) {{
                                    recaptchaDiv.dataset.captchaResolved = 'true';
                                }}

                                // Удаляем любые блокировки для формы, если они есть
                                const forms = document.querySelectorAll('form');
                                forms.forEach(form => {{
                                    if (form.getAttribute('data-captcha-required') === 'true') {{
                                        form.setAttribute('data-captcha-required', 'false');
                                    }}
                                }});

                                console.log('Новый токен капчи вставлен, длина:', token.length);
                            }}
                        """, token)

                        await page.wait_for_timeout(2000)
                        inserted_token = await page.evaluate("""
                            () => {
                                const response = document.querySelector('#g-recaptcha-response');
                                return response ? response.value : null;
                            }
                        """)

                        if inserted_token and len(inserted_token) > 50:
                            logger.info(f"Токен успешно вставлен и проверен, длина: {len(inserted_token)}")

                            # Проверяем статус кнопок после вставки токена
                            buttons_state = await page.evaluate("""
                                () => {
                                    const buttons = Array.from(document.querySelectorAll('button[type="submit"], input[type="submit"]'));
                                    return buttons.map(btn => ({
                                        text: btn.textContent || btn.value || 'КНОПКА',
                                        disabled: btn.disabled,
                                        visible: btn.offsetParent !== null
                                    }));
                                }
                            """)
                            logger.info(f"Состояние кнопок после вставки токена: {buttons_state}")

                            # Если токен вставлен, считаем капчу решенной
                            logger.info("КАПЧА РЕШЕНА! Токен вставлен, кнопки активированы.")
                            task_solved_successfully = True
                            break # Успешное решение, выход из цикла ожидания результата
                        else:
                            logger.error("Токен не найден после вставки! Переход к следующей основной попытке...")
                            await asyncio.sleep(2)
                            break # Выход из цикла ожидания результата, переход к следующей основной попытке

                elif response_get_result.get("status") == "processing":
                    logger.debug(f"Задача {task_id} обрабатывается... (попытка {poll_attempt + 1}, время: {elapsed:.1f}с)")
                else:
                    logger.warning(f"Неожиданный статус от Anti-Captcha: {response_get_result.get('status')}")

                await asyncio.sleep(5)

            if task_solved_successfully:
                return True # Капча успешно решена в этой основной попытке
            else:
                logger.error(f"Не удалось решить задачу {task_id} на попытке {attempt_num + 1} (таймаут или ошибка вставки токена)")
                # Продолжаем к следующей основной попытке, если есть
                await asyncio.sleep(2)
                continue # К следующей основной попытке

        except Exception as e_main_try:
            logger.error(f"Ошибка в основной попытке {attempt_num + 1} решения капчи: {e_main_try}", exc_info=True)
            if attempt_num < max_retries - 1:
                logger.info("Пауза перед следующей попыткой...")
                await asyncio.sleep(5)
            # Если это последняя попытка, цикл завершится и функция вернет False ниже

    logger.error("Все попытки решения капчи исчерпаны.")
    return False

async def run_test_parser():
    """Запускает тестовый парсер с выбором категории Individuals"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context(viewport={"width": 1280, "height": 800})
        page = await context.new_page()

        try:
            logger.info("=== Тестовый парсер CPA Quebec (категория Individuals) ===")
            logger.info(f"Переходим на {BASE_URL}")

            # Переходим на страницу
            await page.goto(BASE_URL, timeout=60000)
            await page.wait_for_load_state("domcontentloaded")

            # Закрываем cookie banner
            try:
                cookie_banner = page.locator("text=Tout refuser")
                if await cookie_banner.is_visible(timeout=5000):
                    await cookie_banner.click()
                    logger.info("Cookie banner закрыт")
                    await page.wait_for_timeout(2000)
            except:
                pass

            # Выбираем категорию "SMEs"
            if not await select_category(page, "SMEs"):
                logger.error("Не удалось выбрать категорию")
                return

            # Обрабатываем капчу
            logger.info("Решаем капчу...")
            if not await solve_captcha_simple(page):
                logger.error("Не удалось решить капчу")
                return

            # Ждем дольше после решения капчи
            logger.info("Ждем стабилизации после решения капчи...")
            await page.wait_for_timeout(5000)

            # Логируем состояние формы перед отправкой
            form_state = await page.evaluate("""
                () => {
                    const checkboxes = Array.from(document.querySelectorAll('input[type="checkbox"]'));
                    const checkedBoxes = checkboxes.filter(cb => cb.checked);

                    return {
                        totalCheckboxes: checkboxes.length,
                        checkedCheckboxes: checkedBoxes.length,
                        checkedDetails: checkedBoxes.map(cb => ({
                            id: cb.id,
                            name: cb.name,
                            value: cb.value,
                            label: cb.closest('label') ? cb.closest('label').textContent.trim() : 'No label'
                        }))
                    };
                }
            """)
            logger.info(f"Состояние формы перед отправкой: {form_state}")

            # ДЕТАЛЬНЫЙ АНАЛИЗ ВСЕХ КНОПОК НА СТРАНИЦЕ
            buttons_analysis = await page.evaluate("""
                () => {
                    const allButtons = Array.from(document.querySelectorAll('button, input[type="submit"], input[type="button"]'));

                    return allButtons.map((btn, index) => {
                        const rect = btn.getBoundingClientRect();
                        const computedStyle = window.getComputedStyle(btn);
                        const form = btn.closest('form');

                        return {
                            index: index,
                            text: btn.textContent || btn.value || 'NO_TEXT',
                            type: btn.type,
                            id: btn.id || 'NO_ID',
                            className: btn.className || 'NO_CLASS',
                            disabled: btn.disabled,
                            visible: btn.offsetParent !== null,
                            position: {
                                left: Math.round(rect.left),
                                top: Math.round(rect.top),
                                width: Math.round(rect.width),
                                height: Math.round(rect.height)
                            },
                            styles: {
                                display: computedStyle.display,
                                visibility: computedStyle.visibility,
                                opacity: computedStyle.opacity
                            },
                            parentForm: form ? {
                                id: form.id || 'FORM_NO_ID',
                                action: form.action || 'NO_ACTION',
                                method: form.method || 'NO_METHOD',
                                hasCheckboxes: form.querySelectorAll('input[type="checkbox"]').length
                            } : 'NO_FORM'
                        };
                    });
                }
            """)

            logger.info("=== ДЕТАЛЬНЫЙ АНАЛИЗ ВСЕХ КНОПОК ===")
            for btn_info in buttons_analysis:
                form_info = btn_info['parentForm']
                if form_info != 'NO_FORM':
                    logger.info(f"Кнопка #{btn_info['index']}: '{btn_info['text']}' | "
                               f"type={btn_info['type']} | id={btn_info['id']} | "
                               f"disabled={btn_info['disabled']} | visible={btn_info['visible']} | "
                               f"pos=({btn_info['position']['left']}, {btn_info['position']['top']}) | "
                               f"form_id={form_info['id']} | form_action={form_info['action']} | checkboxes={form_info['hasCheckboxes']}")
                else:
                    logger.info(f"Кнопка #{btn_info['index']}: '{btn_info['text']}' | "
                               f"type={btn_info['type']} | id={btn_info['id']} | "
                               f"disabled={btn_info['disabled']} | visible={btn_info['visible']} | "
                               f"pos=({btn_info['position']['left']}, {btn_info['position']['top']}) | "
                               f"form=NO_FORM")

            # ПОИСК ФОРМЫ С ЧЕКБОКСАМИ И ЕЕ КНОПОК
            logger.info("=== АНАЛИЗ ФОРМ С ЧЕКБОКСАМИ ===")
            directory_buttons = [btn for btn in buttons_analysis if
                               btn['parentForm'] != 'NO_FORM' and
                               btn['parentForm']['hasCheckboxes'] > 0 and
                               btn['visible'] and not btn['disabled']]

            if directory_buttons:
                logger.info(f"НАЙДЕНО {len(directory_buttons)} КНОПОК В ФОРМАХ С ЧЕКБОКСАМИ:")
                for i, btn in enumerate(directory_buttons):
                    form_info = btn['parentForm']
                    logger.info(f"  Кнопка #{i+1}: '{btn['text']}' | type={btn['type']} | "
                               f"pos=({btn['position']['left']}, {btn['position']['top']}) | "
                               f"form_action={form_info['action']} | checkboxes={form_info['hasCheckboxes']}")
            else:
                logger.warning("НЕ НАЙДЕНО КНОПОК В ФОРМАХ С ЧЕКБОКСАМИ!")

                # Дополнительный поиск всех форм на странице
                logger.info("=== АНАЛИЗ ВСЕХ ФОРМ НА СТРАНИЦЕ ===")
                all_forms = await page.evaluate("""
                    () => {
                        const forms = Array.from(document.querySelectorAll('form'));
                        return forms.map((form, index) => {
                            const checkboxes = form.querySelectorAll('input[type="checkbox"]');
                            const buttons = form.querySelectorAll('button, input[type="submit"], input[type="button"]');

                            return {
                                index: index,
                                id: form.id || 'NO_ID',
                                action: form.action || 'NO_ACTION',
                                method: form.method || 'NO_METHOD',
                                checkboxCount: checkboxes.length,
                                buttonCount: buttons.length,
                                buttons: Array.from(buttons).map(btn => ({
                                    text: btn.textContent || btn.value || 'NO_TEXT',
                                    type: btn.type,
                                    id: btn.id || 'NO_ID',
                                    visible: btn.offsetParent !== null,
                                    disabled: btn.disabled
                                }))
                            };
                        });
                    }
                """)

                for form_info in all_forms:
                    logger.info(f"Форма #{form_info['index']}: id={form_info['id']} | "
                               f"action={form_info['action']} | checkboxes={form_info['checkboxCount']} | "
                               f"buttons={form_info['buttonCount']}")
                    if form_info['buttons']:
                        for btn in form_info['buttons']:
                            logger.info(f"    Кнопка: '{btn['text']}' | type={btn['type']} | "
                                       f"visible={btn['visible']} | disabled={btn['disabled']}")

            # Нажимаем кнопку поиска
            logger.info("Нажимаем кнопку поиска...")

            # Ищем и кликаем на кнопку поиска (французская или английская версия)
            try:
                logger.info("Ищем кнопку поиска для клика...")

                # Расширенный список селекторов для поиска кнопки
                selectors = [
                    "text=Rechercher",  # Французская версия
                    "text=Search",  # Английская версия
                    "button:has-text('Rechercher')",
                    "button:has-text('Search')",
                    "input[value='Rechercher']",
                    "input[value='Search']",
                    "button[type='submit']:has-text('Rechercher')",
                    "button[type='submit']:has-text('Search')",
                    "input[type='submit'][value*='Rechercher']",
                    "input[type='submit'][value*='Search']",
                    # Если ничего не найдено, пробуем любые submit кнопки (но исключаем reset)
                    "button[type='submit']:not(:has-text('Reset')):not(:has-text('Réinitialiser'))",
                    "input[type='submit']:not([value*='Reset']):not([value*='Réinitialiser'])"
                ]

                button_found = False
                for i, selector in enumerate(selectors):
                    try:
                        logger.info(f"Пробуем селектор #{i+1}: {selector}")
                        button = page.locator(selector).first
                        button_count = await button.count()

                        if button_count > 0:
                            # Получаем текст кнопки для проверки
                            try:
                                button_text = await button.inner_text() if await button.is_visible() else ""
                                button_value = await button.get_attribute("value") or ""
                                combined_text = f"{button_text} {button_value}".strip()
                            except:
                                combined_text = "Unknown"

                            # Проверяем, что это не кнопка сброса
                            if any(word in combined_text.lower() for word in ["reset", "réinitialiser", "clear"]):
                                logger.info(f"Пропускаем кнопку сброса: '{combined_text}'")
                                continue

                            logger.info(f"Найдена кнопка: '{combined_text}' с селектором: {selector}")

                            # Проверяем видимость кнопки
                            is_visible = await button.is_visible()
                            is_enabled = await button.is_enabled()

                            if is_visible and is_enabled:
                                logger.info(f"Кнопка видима и активна, кликаем: '{combined_text}'")

                                # Получаем позицию кнопки до прокрутки
                                initial_position = await button.bounding_box()
                                logger.info(f"Начальная позиция кнопки: {initial_position}")

                                # Прокручиваем к кнопке
                                await button.scroll_into_view_if_needed()
                                await page.wait_for_timeout(1000)  # Ждем завершения прокрутки

                                # Проверяем позицию после прокрутки
                                new_position = await button.bounding_box()
                                logger.info(f"Позиция после прокрутки: {new_position}")

                                # Проверяем, что кнопка все еще видима и в области просмотра
                                is_still_visible = await button.is_visible()
                                is_in_viewport = await button.is_in_viewport()

                                logger.info(f"После прокрутки: visible={is_still_visible}, in_viewport={is_in_viewport}")

                                if not is_still_visible or not is_in_viewport:
                                    logger.warning("Кнопка сместилась после прокрутки, пробуем JavaScript клик")
                                    # Используем JavaScript клик как более надежный метод
                                    js_click_result = await page.evaluate(f"""
                                        (selector) => {{
                                            const button = document.querySelector('{selector.replace("text=", "").replace("button:", "button").replace(":has-text", "")}');
                                            if (!button) {{
                                                // Пробуем найти по тексту
                                                const buttons = Array.from(document.querySelectorAll('button, input[type="submit"]'));
                                                const targetButton = buttons.find(btn =>
                                                    (btn.textContent || btn.value || '').includes('{combined_text}')
                                                );
                                                if (targetButton) {{
                                                    targetButton.click();
                                                    console.log('JavaScript клик выполнен по тексту');
                                                    return true;
                                                }}
                                                return false;
                                            }}
                                            button.click();
                                            console.log('JavaScript клик выполнен по селектору');
                                            return true;
                                        }}
                                    """, selector)

                                    if js_click_result:
                                        logger.info("JavaScript клик успешен")
                                        button_found = True
                                        break
                                    else:
                                        logger.error("JavaScript клик не удался")
                                        continue

                            if is_visible and is_enabled:
                                logger.info(f"Кнопка найдена, но не видима или неактивна: visible={is_visible}, enabled={is_enabled}")
                                # Пробуем JavaScript клик даже для невидимой кнопки
                                logger.info("Пробуем JavaScript клик для невидимой/неактивной кнопки...")
                                js_click_result = await page.evaluate(f"""
                                    () => {{
                                        const buttons = Array.from(document.querySelectorAll('button, input[type="submit"]'));
                                        const targetButton = buttons.find(btn =>
                                            (btn.textContent || btn.value || '').includes('{combined_text}')
                                        );
                                        if (targetButton) {{
                                            // Принудительно активируем кнопку
                                            targetButton.disabled = false;
                                            targetButton.style.display = 'block';
                                            targetButton.style.visibility = 'visible';
                                            targetButton.click();
                                            console.log('JavaScript клик на невидимую кнопку выполнен');
                                            return true;
                                        }}
                                        return false;
                                    }}
                                """)

                                if js_click_result:
                                    logger.info("JavaScript клик на невидимую кнопку успешен")
                                    button_found = True
                                    break
                                else:
                                    logger.warning("JavaScript клик на невидимую кнопку не удался")

                    except Exception as selector_error:
                        logger.debug(f"Ошибка с селектором {selector}: {selector_error}")
                        continue

                if not button_found:
                    logger.error("НЕ УДАЛОСЬ НАЙТИ ПОДХОДЯЩУЮ КНОПКУ ПОИСКА!")

                    # Попробуем отправить форму CPA напрямую через JavaScript
                    logger.info("Пробуем отправить форму CPA напрямую через JavaScript...")
                    js_submit_success = await page.evaluate("""
                        () => {
                            // Ищем форму с чекбоксами (основная форма поиска CPA)
                            const cpaForm = document.getElementById('FindACPABottinForm') ||
                                          document.querySelector('form[action*="FindACPA"]') ||
                                          document.querySelector('form[action*="BottinForm"]');

                            if (cpaForm) {
                                console.log('Найдена форма CPA:', cpaForm.id, cpaForm.action);

                                // Проверяем наличие чекбоксов
                                const checkboxes = cpaForm.querySelectorAll('input[type="checkbox"]:checked');
                                console.log('Отмеченных чекбоксов:', checkboxes.length);

                                // Отправляем форму
                                try {
                                    cpaForm.submit();
                                    console.log('Форма CPA отправлена через submit()');
                                    return true;
                                } catch (e) {
                                    console.error('Ошибка при submit():', e);

                                    // Пробуем создать и отправить событие submit
                                    try {
                                        const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                                        cpaForm.dispatchEvent(submitEvent);
                                        console.log('Отправлено событие submit');
                                        return true;
                                    } catch (e2) {
                                        console.error('Ошибка при dispatchEvent:', e2);
                                        return false;
                                    }
                                }
                            } else {
                                console.error('Форма CPA не найдена');

                                // Fallback: ищем любую submit кнопку как раньше
                                const buttons = Array.from(document.querySelectorAll('button[type="submit"], input[type="submit"]'));

                                for (const btn of buttons) {
                                    const text = (btn.textContent || btn.value || '').toLowerCase();

                                    // Исключаем кнопки сброса
                                    if (text.includes('reset') || text.includes('réinitialiser') || text.includes('clear')) {
                                        continue;
                                    }

                                    // Проверяем видимость
                                    if (btn.offsetParent !== null && !btn.disabled) {
                                        console.log('JavaScript клик на кнопку:', btn.textContent || btn.value);
                                        btn.click();
                                        return true;
                                    }
                                }

                                console.log('Подходящая кнопка для JavaScript клика не найдена');
                                return false;
                            }
                        }
                    """)

                    if js_submit_success:
                        logger.info("JavaScript отправка формы выполнена успешно!")
                        button_found = True
                    else:
                        logger.error("Даже JavaScript клик не сработал!")
                        # Делаем скриншот для диагностики
                        await page.screenshot(path="no_search_button.png", full_page=True)
                        logger.info("Сделан скриншот: no_search_button.png")
                        return

                # Если кнопка была нажата, ждем навигации
                if button_found:
                    logger.info("Кнопка поиска нажата, ожидаем навигацию...")
                    try:
                        # Ждем навигации с таймаутом
                        await page.wait_for_navigation(timeout=30000, wait_until="domcontentloaded")
                        logger.info("Навигация завершена успешно")
                    except Exception as nav_error:
                        logger.warning(f"Навигация не произошла или завершилась с ошибкой: {nav_error}")
                        # Ждем немного и проверяем, изменился ли URL
                        await page.wait_for_timeout(5000)
                        current_url = page.url
                        if "search" in current_url.lower() or current_url != BASE_URL:
                            logger.info(f"URL изменился на: {current_url}, считаем навигацию успешной")
                        else:
                            logger.warning("URL не изменился, возможно навигация не произошла")

            except Exception as click_error:
                logger.error(f"Ошибка при поиске и клике на кнопку поиска: {click_error}")
                # Делаем скриншот страницы для диагностики
                try:
                    await page.screenshot(path="search_button_error.png", full_page=True)
                    logger.info("Сделан скриншот ошибки: search_button_error.png")
                except:
                    pass
                return

            logger.info("Кнопка поиска нажата, ожидаем результаты...")

            # Ждем загрузки
            await page.wait_for_load_state("domcontentloaded")
            await page.wait_for_timeout(5000)

            # Анализируем результаты
            current_url = page.url
            logger.info(f"URL после поиска: {current_url}")

            # Получаем информацию о результатах
            results_info = await page.evaluate("""
                () => {
                    const pageText = document.body.innerText;

                    // Ищем текст о количестве результатов
                    const resultMatches = pageText.match(/(\\d+)\\s*-\\s*(\\d+)\\s*of\\s*(\\d+)\\s*result/i);

                    return {
                        url: window.location.href,
                        pageText: pageText.substring(0, 1000),
                        hasResultsText: pageText.toLowerCase().includes('result'),
                        resultMatches: resultMatches,
                        emailCount: document.querySelectorAll('a[href^="mailto"]').length,
                        phoneCount: document.querySelectorAll('a[href^="tel"]').length
                    };
                }
            """)

            logger.info(f"Анализ результатов: {results_info}")

            if results_info['resultMatches']:
                logger.info(f"Найдены результаты: {results_info['resultMatches'][0]}")
                logger.info(f"Email ссылок: {results_info['emailCount']}")
                logger.info(f"Телефонных ссылок: {results_info['phoneCount']}")
            else:
                logger.warning("Результаты поиска не найдены или равны 0")
                logger.info("Текст страницы:")
                logger.info(results_info['pageText'])

            # Ждем для анализа
            input("\nПроанализируйте результаты в браузере и нажмите Enter...")

        except Exception as e:
            logger.error(f"Ошибка: {e}")
        finally:
            if anticaptcha_session and not anticaptcha_session.closed:
                await anticaptcha_session.close()
            await browser.close()

if __name__ == "__main__":
    asyncio.run(run_test_parser())